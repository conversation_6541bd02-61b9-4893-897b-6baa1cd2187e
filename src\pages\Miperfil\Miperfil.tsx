import React, { useEffect } from 'react';
import { Card, Container } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { FA6 } from '@/config/icons/iconUtils';
import { PageMetadata } from '@/hoc/withPageMetadata';
import { PERMISSIONS } from '@/config/authConfig';
import { useDatosPersonales } from '@/hooks/useDatosPersonales';

const Miperfil: React.FC & { pageMetadata: PageMetadata } = () => {
  const datosPersonales = useDatosPersonales();
  const navigate = useNavigate();

  useEffect(() => {
    const refreshData = async () => {
      try {
        await datosPersonales.refetchDatosPersonales();
      } catch (error) {
        console.error('[Miperfil] Error refreshing personal data:', error);
      }
    };

    refreshData();
  }, []);

  const displayData = {
    nombre: datosPersonales.nombre,
    fechaNacimiento: datosPersonales.fechaNacimiento,
    correoElectronico: datosPersonales.email,
    numeroTelefono: datosPersonales.telefono,
    domicilioCalle: datosPersonales.domicilio,
    localidad: datosPersonales.localidad,
    fechaContrato: datosPersonales.fechaContrato,
    codigoCliente: datosPersonales.codigo,
    legajo: datosPersonales.legajo,
    estado: datosPersonales.estado,
    documento: datosPersonales.documento,
    cuil: datosPersonales.cuil,
    genero: datosPersonales.genero,
    edad: datosPersonales.edad,
    limiteCredito: datosPersonales.limiteCredito
  };

  if (!datosPersonales.hasDatosPersonales) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="text-center">
          <FA6.FaTriangleExclamation size={32} className="text-warning" />
          <p className="mt-2">No se encontraron datos personales.</p>
          <button className="btn btn-primary mt-2" onClick={() => window.location.reload()}>Reintentar</button>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid>
      {datosPersonales.hasDatosPersonales && (
        <div className="row g-3">
          {/* Foto de Perfil */}
          <div className="col-12">
            <Card className="border-0 shadow-sm">
              <Card.Body className="p-3">
                <div className="d-flex align-items-center">
                  <div className="me-3">
                    <div className="position-relative">
                      <div
                        className="rounded-circle bg-light border d-flex align-items-center justify-content-center"
                        style={{ width: '80px', height: '80px' }}
                      >
                        <FA6.FaUser className="text-muted" size={32} />
                      </div>
                    </div>
                  </div>
                  <div className="flex-grow-1">
                    <h6 className="mb-1 fw-bold">{displayData.nombre}</h6>
                    <p className="text-muted mb-2 small">{displayData.correoElectronico}</p>
                    <div className="d-flex align-items-center">

                      <small className='p-2'>Cod. Cliente: {displayData.codigoCliente}</small>

                       {displayData.estado === 'activo' ? (
                        <span className="badge bg-success me-2">
                          <FA6.FaCircleCheck className="me-1" size={10} />
                          Activo
                        </span>
                      ) : (
                        <span className="badge bg-danger me-2">
                          <FA6.FaCircleXmark className="me-1" size={10} />
                          Inactivo
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </div>

          {/* Información Laboral */}
          <div className="col-12">
            <Card className="border-0 shadow-sm">
              <Card.Body className="p-3">
                <h6 className="card-title text-primary mb-2 border-bottom pb-2">
                  <FA6.FaBriefcase className="me-2" />
                  Información Laboral
                </h6>

                <div className="row g-2">
                  <div className="col-md-4">
                    <div className="py-2 px-2 bg-light rounded-3 d-flex align-items-center">
                      <FA6.FaFileLines className="text-primary me-2" size={14} />
                      <div>
                        <div className="text-muted small">Legajo</div>
                        <div className="fw-bold small">{displayData.legajo}</div>
                      </div>
                    </div>
                  </div>

                  <div className="col-md-4">
                    <div className="py-2 px-2 bg-light rounded-3 d-flex align-items-center">
                      <FA6.FaCalendarCheck className="text-primary me-2" size={14} />
                      <div>
                        <div className="text-muted small">Fecha de Ingreso</div>
                        <div className="d-flex align-items-center">
                          <span className="fw-bold small me-2">{displayData.fechaContrato}</span>
                          {displayData.fechaContrato !== 'Sin información' && datosPersonales.antiguedad && (
                            <span className="badge bg-info">{datosPersonales.antiguedad}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="col-md-4">
                    <div className="py-2 px-2 bg-light rounded-3 d-flex align-items-center">
                      <FA6.FaCreditCard className="text-primary me-2" size={14} />
                      <div>
                        <div className="text-muted small">Límite de Crédito</div>
                        <div className="fw-bold small">${displayData.limiteCredito}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </div>

          {/* Datos Personales */}
          <div className="col-lg-6">
            <Card className="border-0 h-100 shadow-sm">
              <Card.Body className="p-3">
                <h6 className="card-title text-primary mb-2 border-bottom pb-2">
                  <FA6.FaUser className="me-2" />
                  Datos Personales
                </h6>

                <div className="d-flex flex-column gap-2">
                  <div className="py-2 px-2 bg-light rounded-3 d-flex align-items-center">
                    <FA6.FaUser className="text-primary me-2" size={14} />
                    <div className="flex-grow-1">
                      <div className="text-muted small">Nombre Completo</div>
                      <div className="fw-bold small">{displayData.nombre}</div>
                    </div>
                  </div>

                  <div className="row g-2">
                    <div className="col-md-6">
                      <div className="py-2 px-2 bg-light rounded-3 d-flex align-items-center">
                        <FA6.FaIdCard className="text-primary me-2" size={14} />
                        <div>
                          <div className="text-muted small">Documento</div>
                          <div className="fw-bold small">{displayData.documento}</div>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="py-2 px-2 bg-light rounded-3 d-flex align-items-center">
                        <FA6.FaPerson className="text-primary me-2" size={14} />
                        <div>
                          <div className="text-muted small">Género</div>
                          <div className="fw-bold small">{displayData.genero}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="py-2 px-2 bg-light rounded-3 d-flex align-items-center">
                    <FA6.FaCakeCandles className="text-primary me-2" size={14} />
                    <div>
                      <div className="text-muted small">Fecha de Nacimiento</div>
                      <div className="d-flex align-items-center">
                        <span className="fw-bold small me-2">{displayData.fechaNacimiento}</span>
                        {displayData.edad && (
                          <span className="badge bg-secondary">{displayData.edad} años</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </div>

          {/* Información de Contacto */}
          <div className="col-lg-6">
            <Card className="border-0 h-100 shadow-sm">
              <Card.Body className="p-3">
                <h6 className="card-title text-primary mb-2 border-bottom pb-2">
                  <FA6.FaAddressBook className="me-2" />
                  Información de Contacto
                </h6>

                <div className="d-flex flex-column gap-2">
                  <div className="py-2 px-2 bg-light rounded-3 d-flex align-items-center">
                    <FA6.FaPhone className="text-primary me-2" size={14} />
                    <div>
                      <div className="text-muted small">Número de Teléfono</div>
                      <div className="fw-bold small">{displayData.numeroTelefono}</div>
                    </div>
                  </div>

                  <div className="py-2 px-2 bg-light rounded-3 d-flex align-items-center">
                    <FA6.FaHouseChimney className="text-primary me-2" size={14} />
                    <div>
                      <div className="text-muted small">Domicilio</div>
                      <div className="fw-bold small">{displayData.domicilioCalle}</div>
                    </div>
                  </div>

                  <div className="py-2 px-2 bg-light rounded-3 d-flex align-items-center">
                    <FA6.FaLocationDot className="text-primary me-2" size={14} />
                    <div>
                      <div className="text-muted small">Localidad</div>
                      <div className="fw-bold small">{displayData.localidad}</div>
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </div>

          {/* Ver Movimientos */}
          <div className="col-12 py-4">
            <div className="d-flex justify-content-center">
              <div className="col-md-4">
                <Card className="border-0 shadow-sm">
                  <Card.Body className="p-3">
                    <div
                      className="d-flex align-items-center justify-content-between py-2 px-3 bg-light rounded-3"
                      onClick={() => navigate('/main/estadodecuenta')}
                      style={{
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        userSelect: 'none'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#e3f2fd';
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#f8f9fa';
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                      onMouseDown={(e) => {
                        e.currentTarget.style.transform = 'translateY(0) scale(0.98)';
                      }}
                      onMouseUp={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px) scale(1)';
                      }}
                    >
                      <div className="d-flex align-items-center primary">
                        <FA6.FaMoneyBillTransfer className="text-primary me-3" size={20} />
                        <div>
                          <div className="fw-bold small text-dark">Ver movimientos</div>
                          <div className="text-muted small">Consulta tu estado de cuenta</div>
                        </div>
                      </div>
                      <FA6.FaChevronRight className="text-muted" size={14} />
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </div>
          </div>
        </div>
      )}
    </Container>
  );
};

Miperfil.pageMetadata = {
  title: 'Datos Personales',
  description: 'Datos Personales',
  icon: <FA6.FaUserTie className="fs-6" />,
  showInMenu: true,
  menuOrder: 1,
  path: '/main/miperfil',
  requiresAuth: true,
  section: 'Perfil',
  permissions: {
    requiredPermission: PERMISSIONS.VIEW,
    resourceId: 'miperfil'
  }
};

export default Miperfil;
