//
// This style will override bootstrap style  and custom styles
//
html[dir="rtl"] {
  // Include bootstrap rtl mixin
  @import "rtl-spacing";
  @import "rtl-components";

  body {
    text-align: right;
    direction: rtl;
  }

  .customizer-btn {
    right: unset;
    left: 20px;
  }

  [data-sidebartype="mini-sidebar"] {
    .left-sidebar {
      &:hover {
        .nav-small-cap {
          text-align: right;
        }

        .sidebar-item {
          .two-level {
            .sidebar-item {
              .sidebar-link {
                padding: $rtl-sidebar-first-level-link-padding !important;
              }
            }
          }

          .three-level {
            .sidebar-item {
              .sidebar-link {
                padding: $rtl-sidebar-first-level-link-padding !important;
              }
            }
          }
        }
      }
    }
  }

  .sidebar-nav {
    ul {
      .sidebar-item {

        .sidebar-link {
          .badge {
            left: 30px;
            right: auto;
            position: absolute;
          }
        }
      }
    }
  }

  .page-wrapper {
    margin-left: 0;
    margin-right: $sidebar-width-full;

    @include media-breakpoint-down(xl) {
      margin-right: 0;
    }
  }

  // Custom Control
  //
  .custom-select {
    padding: 0.375rem 0.75rem 0.375rem 1.75rem;
    background: url(../../html/assets/images/custom-select.png) left 0.35rem center no-repeat $white;
  }

  .list-group {
    padding: 0;
  }

  .lstick {
    margin-right: -20px;
    margin-left: 18px;
  }

  .dropdown-menu {
    text-align: right;
  }

  // Bootstrap component setting
  //
  .navbar-nav,
  .nav,
  .list-unstyled,
  .pagination {
    padding-right: 0;
  }

  //
  // Topbar
  //
  .topbar .navbar-collapse {
    padding: 0 0 0 10px;
  }

  .topbar .navbar .navbar-nav .nav-item.dropdown .dropdown-menu-end {
    right: auto;
    left: 15px;
  }

  .user-profile .profile-img {
    margin-right: 30px;
    margin-left: auto;
  }

  .user-profile .profile-text>a:after {
    left: 20px;
    right: auto;
  }

  .topbar .top-navbar .navbar-header .navbar-brand .logo-icon {
    margin-right: 0;
  }

  .topbar .dropdown-menu.dropdown-menu-right .with-arrow {
    left: 0;
    right: auto;
  }

  .topbar .dropdown-menu.dropdown-menu-right .with-arrow>span,
  .search-box .app-search .srh-btn {
    left: 20px;
    right: auto;
  }

  .dropdown-menu .flag-icon {
    margin-left: 5px;
    margin-right: 0;
  }

  //
  // Sidebar
  //
  .sidebar-nav .user-profile>.sidebar-link img {
    margin-right: 0;
    margin-left: 10px;
  }

  .sidebar-nav ul .sidebar-item .first-level {
    padding-left: 0 !important;
  }

  .sidebar-nav .has-arrow::after {
    margin-left: 0;
    margin-right: 10px;
    right: auto;
    left: 15px;
  }

  .sidebar-nav .user-profile .first-level {
    padding-left: 0 !important;
    padding-right: 48px !important;
  }

  .sidebar-nav ul .sidebar-item .sidebar-link .badge {
    left: 34px;
    right: auto;
    top: 16px;
  }


  .brand-logo {
    margin-right: 6px;
  }

  .upgrade-plan {
    &::after {
      background-position: left;
    }
  }

  @include media-breakpoint-up(xl) {
    &[data-layout=vertical] {
      body[data-sidebartype=mini-sidebar] {
        .user-profile {
          .profile-img {
            margin-left: 0;
            margin-right: 9px;
          }
        }
      }
    }
  }



  @include media-breakpoint-up(md) {
    #main-wrapper[data-layout="vertical"] {
      &[data-sidebartype="mini-sidebar"] {
        .left-sidebar {
          &:hover {
            .sidebar-nav ul .sidebar-item .first-level {
              padding-left: 0;
              padding-right: 33px;
            }
          }
        }
      }
    }
  }


  .sidebar-nav {
    ul {
      .sidebar-item {
        .first-level {
          .sidebar-item .sidebar-link {
            padding: $rtl-sidebar-first-level-link-padding;
          }
        }
      }
    }
  }

  //
  // Different Sidebars
  //
  #main-wrapper {

    // Sidebartype=full
    //
    &[data-sidebartype="full"] {
      .page-wrapper {
        margin-right: $sidebar-width-full;
        margin-left: 0;
      }
    }

    // Sidebartype=iconbar
    //
    &[data-sidebartype="iconbar"] {
      .page-wrapper {
        margin-right: $sidebar-width-iconbar;
        margin-left: 0;
      }
    }

    // Sidebartype=overlay
    //
    &[data-sidebartype="overlay"] {
      .left-sidebar {
        right: -$sidebar-width-full;
        left: auto;
      }

      &.show-sidebar {
        .left-sidebar {
          right: 0;
          left: auto;
        }
      }
    }
  }

  // Above  Tablet
  //
  @include media-breakpoint-up(md) {
    #main-wrapper {


      // sidebartype=full & sidebartype=overlay
      //
      &[data-sidebar-position="fixed"][data-sidebartype="full"],
      &[data-sidebar-position="fixed"][data-sidebartype="overlay"] {
        .topbar .top-navbar .navbar-collapse {
          margin-right: $sidebar-width-full;
          margin-left: 0;
        }
      }

      // sidebartype=mini-sidebar
      //
      &[data-sidebar-position="fixed"][data-sidebartype="mini-sidebar"] {
        .topbar .top-navbar .navbar-collapse {
          margin-right: $sidebar-width-mini;
          margin-left: 0;
        }
      }

      // sidebartype=iconbar
      //
      &[data-sidebar-position="fixed"][data-sidebartype="iconbar"] {
        .topbar .top-navbar .navbar-collapse {
          margin-right: $sidebar-width-iconbar;
          margin-left: 0;
        }
      }

      // sidebartype=mini-sidebar
      //
      &[data-sidebartype="mini-sidebar"] {
        .page-wrapper {
          margin-left: 0;
          margin-right: $sidebar-width-mini;
        }


        .user-profile .profile-img {
          margin-right: 9px;
          margin-left: 0;
        }

        .left-sidebar {

          &:hover {
            .sidebar-nav ul .sidebar-item .sidebar-link {
              padding: $rtl-sidebar-link-padding;
            }

            .sidebar-nav ul .sidebar-item .first-level .sidebar-item .sidebar-link {
              padding: $rtl-sidebar-first-level-link-padding;
            }
          }
        }
      }
    }
  }

  @include media-breakpoint-down(sm) {
    #main-wrapper {

      // sidebartype=mini-sidebar
      //
      &[data-sidebartype="mini-sidebar"] {
        .left-sidebar {
          right: -$sidebar-width-full;
          left: auto;
        }
      }

      &.show-sidebar {
        .left-sidebar {
          right: 0;
          left: auto;

          .sidebar-footer {
            right: 0;
            left: auto;
          }
        }
      }
    }
  }


  .sidebar-nav {
    .has-arrow::after {
      margin-left: 0;
      margin-right: 10px;
      right: auto;
      left: 15px;
    }

  }



  &[data-layout="horizontal"] {
    .page-wrapper {
      margin: 0 auto !important;
    }

    .sidebar-nav #sidebarnav>.sidebar-item>.has-arrow:after {
      right: 5px;
    }
  }


  @media screen and (max-width: 1299.98px) {
    .brand-logo {
      margin-right: 0px !important;
    }

    .left-sidebar {
      right: -$left-part-width;
    }

    #main-wrapper {
      &.show-sidebar {
        .left-sidebar {
          right: 0;
        }
      }
    }
  }


  .toast-onload {
    right: unset !important;
    left: 24px !important;
  }

}