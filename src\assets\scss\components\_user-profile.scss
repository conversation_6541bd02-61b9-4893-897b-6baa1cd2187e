// ----------------------------------------------
// User Profile Component Styles
// ----------------------------------------------

.user-profile {
  // Use full available width
  width: 100%;
  padding: 0 15px; // Reduced padding for better width utilization
  box-sizing: border-box;

  .profile-text {
    width: $user-profile-text-width-full;
    max-width: $user-profile-text-max-width;
    margin: 0 auto; // Center the text container

    > a {
      padding: $user-profile-text-padding;
      background: $user-profile-text-bg;
      width: 100%; // Use full width of container
      min-width: 0; // Allow shrinking for text truncation
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;
      border-radius: 4px;
      transition: all 0.2s ease-in-out;
      box-sizing: border-box; // Include padding in width calculation
      text-align: center; // Center the text

      &:hover {
        background: rgba(0, 0, 0, 0.7);
      }

      &:after {
        position: absolute;
        right: 20px;
        top: 15px;
      }
    }
  }
  
  .profile-img {
    width: $user-profile-img-size;
    margin: 0 auto;
    padding: $user-profile-img-padding;
    position: relative;

    img {
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      background: #ffffff;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      width: 100%;
      height: 100%;
      object-fit: cover;

      &:hover {
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        transform: translateY(-1px);
      }
    }
  }
  
  .dropdown-menu {
    left: 50% !important;
    right: auto;
    transform: translateX(-50%); // Center the dropdown
    width: $user-profile-dropdown-width;
    margin: 0;
    border-radius: $user-profile-dropdown-border-radius;
    box-shadow: $user-profile-dropdown-box-shadow;
    border: $user-profile-dropdown-border;
    z-index: 1060; // Higher than sidebar navigation
    
    .dropdown-item {
      padding: 12px 20px;
      transition: all 0.2s ease-in-out;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
      
      &:active {
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }
  // Sidebar visibility management when dropdown is open
  &.dropdown-open {
    .dropdown-menu {
      display: block !important;
    }
  }
}

// Sidebar navigation visibility management
.left-sidebar {
  .sidebar-nav {
    transition: opacity 0.3s ease-in-out, filter 0.3s ease-in-out;
  }

  // When user profile dropdown is open, dim other navigation elements
  &.user-dropdown-open {
    .sidebar-nav {
      opacity: 0.3;
      filter: blur(1px);
      pointer-events: none; // Prevent interaction with dimmed elements
    }

    .user-profile {
      opacity: 1;
      filter: none;
      pointer-events: auto; // Keep user profile interactive
      z-index: 1050; // Ensure it stays above dimmed content
      position: relative;
    }
  }
}

// Backdrop effect for better focus
.user-profile-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

// Dark theme adjustments
[data-bs-theme="dark"] {
  .user-profile {
    .profile-img {
      img {
        border: 4px solid rgba(255, 255, 255, 0.4);
        background: #ffffff;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

        &:hover {
          border-color: rgba(255, 255, 255, 0.6);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }
      }
    }

    .dropdown-menu {
      .dropdown-item {
        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        &:active {
          background-color: rgba(255, 255, 255, 0.15);
        }
      }
    }
  }
}

// Mini sidebar adjustments
[data-sidebartype="mini-sidebar"] {
  .left-sidebar {
    &:hover {
      .user-profile {
        width: 100%;
        padding: 0 15px; // Restore full padding on hover

        .profile-img {
          margin: 0 auto; // Keep centered
        }

        .profile-text {
          display: block;
          width: $user-profile-text-width-full;
          max-width: $user-profile-text-max-width;
        }
      }
    }

    .user-profile {
      width: 100%;
      padding: 0 11px; // Reduced padding for mini mode

      .profile-text {
        display: none;
      }

      .profile-img {
        margin: 0 auto; // Keep centered in mini mode
      }
    }
  }
}


