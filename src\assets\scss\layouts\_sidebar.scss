// ----------------------------------------------
// SideBar Style
// ----------------------------------------------
.left-sidebar {
  width: $sidebar-width-full;
  flex-shrink: 0;
  background: $white;
  z-index: 10;
  transition: 0.2s ease-in;
  position: fixed;
  left: 0;
  right: 0;
  height: 100%;
  top: $headerHeight;
  box-shadow: $box-shadow;

  .sidebartoggler {
    color: var(--bs-dark-text-emphasis);
  }

  .scroll-sidebar {
    overflow-y: auto;
    height: calc(100vh - 80px);

    .simplebar-track.simplebar-horizontal {
      visibility: hidden !important;
    }
  }
}

.simplebar-scrollbar:before {
  background: rgba(0, 0, 0, 0.5) !important;
}

.brand-logo {
  min-height: $headerHeight;
  width: $sidebar-width-full;
}

// -----------------------------------
// Sidebar list
// -----------------------------------
#sidebarnav{
  padding: 0 15px;
}
.sidebar-nav {
  ul {
    .sidebar-item {
      width: 100%;
      .sidebar-link {
        display: flex;
        font-size: 15px;
        white-space: nowrap;
        align-items: center;
        line-height: 25px;
        position: relative;
        padding: 12px 16px;
        text-decoration: none;
        font-weight: $font-weight-normal;
        color: var(--bs-heading-color) !important;
        border-radius: 26px;
        gap: 12px;

        &:hover{
          color: var(--bs-secondary) !important;
        }

        &.sublink{
          font-weight: $font-weight-normal;
        }

        .side-badge.badge {
          position: absolute;
          right: 32px;
          top: 13px;
          padding: 3px 10px;
          border-radius: 4px;
          font-size: 12px;
        }

        .hide-menu {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: normal;
        }

        iconify-icon {
          flex-shrink: 0;
          font-size: 20px;
          display: flex;
          align-items: center;
        }


        &:hover {
          color: var(--bs-muted);
          opacity: 1;

          &.link-disabled{
            opacity: 0.38;
          }
        }
      }

      &.sidebar-profile{
        .sidebar-link{
          &.active{
            background-color: transparent !important;
          }
        }
      }


      .sidebar-link{
        &.active{
          background-color: var(--bs-secondary);
          border-color: var(--bs-secondary);
          color: var(--bs-white) !important;
        }
        &.link-disabled{
          background-color: transparent;
          border-color:transparent;
        }
      }

      .link-disabled {
        opacity: $link-disabled;
      }

      .first-level {
        margin-top: 10px;

        .sidebar-item {
          transition: all 0.4s ease-in-out;
          border-bottom: 0;

          .sidebar-link {
            iconify-icon {
              display: none;
              font-size: 15px;
            }

            &:hover {
              color: var(--bs-white);
              opacity: 1;
            }
          }

          &>.sidebar-link {
            font-size: 15px;
            padding: 10px 20px
          }

          &:last-child {
            margin-bottom: 16px;
          }
        }
      }

      .first-level {
        .sidebar-item {
          .sidebar-link.active {
            color: var(--bs-secondary) !important ;
            opacity: 1;
            background-color: transparent;
          }
        }
      }

      .two-level {
        .sidebar-item {
          .sidebar-link {
            padding: 10px 4px 10px 31px;
          }
        }
      }

      .three-level {
        .sidebar-item {
          .sidebar-link {
            padding: 10px 4px 10px 56px;
          }
        }
      }

      &.selected>.sidebar-link{
        background-color: var(--bs-secondary);
        border-color: var(--bs-secondary);
        color: var(--bs-white) !important;
      }
      
    }

  }

  .sidebar-list {
    .sidebar-list-item {
      padding: 8px 0;
    }
  }

  .devider {
    margin: 7px 0;
    border-top: 1px solid var(--bs-white) ;
    opacity: 0.08;
  }

}

.nav-small-cap {
  margin-top: 24px;
  color: var(--bs-heading-color);
  font-size: 13px;
  font-weight: 600;
  padding: 0px 0px 8px;
  line-height: 19px;
  text-transform: uppercase;

  .nav-small-cap-icon {
    display: none;
  }
}


.collapse.in {
  display: block;
}

@media (max-width: 1199px) {
  .brand-logo {
    width: auto;
  }
}

// -----------------------------------
// Sidebar Type : Minisidebar
// -----------------------------------

// About LG Screen
@include media-breakpoint-up(xl) {
  [data-layout="vertical"] {
    [data-sidebartype="mini-sidebar"] {
      .brand-logo {
        width: $sidebar-width-mini;
      }

      .left-sidebar {
        width: $sidebar-width-mini;

        .sidebar-profile .sidebar-link,
        .sidebar-nav ul .sidebar-item .first-level .sidebar-item>.sidebar-link {
          justify-content: center;
        }

        .sidebar-nav ul .sidebar-item .two-level .sidebar-item .sidebar-link{
          padding: 10px 12px 10px 12px;
        }

        .sidebar-nav ul .sidebar-item .first-level .sidebar-item .sidebar-link iconify-icon {
          font-size: 14px;
        }



        &:hover {
          width: $sidebar-width-full;
          position: fixed;
          height: 100vh;

          
          .sidebar-nav ul .sidebar-item .two-level .sidebar-item .sidebar-link{
            padding: 12px 4px 12px 25px;
          }
          .sidebar-nav ul .sidebar-item .three-level .sidebar-item .sidebar-link{
            padding: 12px 4px 12px 50px;
          }

          .sidebar-profile .sidebar-link,
          .sidebar-nav ul .sidebar-item .first-level .sidebar-item>.sidebar-link {
            justify-content: start;
          }

          .sidebar-nav ul .sidebar-item .first-level .sidebar-item .sidebar-link iconify-icon {
            font-size: 15px;
          }

          .nav-small-cap {
            text-align: left;
          }

          .sidebar-nav {
            ul .sidebar-item {
              .first-level {
                .sidebar-link {
                  iconify-icon {
                    display: none;
                  }
                }
              }
            }
          }
        }
      }

      .nav-small-cap {
        text-align: center;
      }

      .sidebar-nav {
        ul .sidebar-item {
          .first-level {
            padding-left: 0;
          }

          .sidebar-link {
            iconify-icon {
              display: block;
              width: auto;
            }
          }
        }
      }
    }
  }
}

//Apps Menu
#mobilenavbar{
  .brand-logo{
    padding:0 0;
  }
  .sidebar-nav{
    ul{
      .sidebar-item{
        .sidebar-link{
          color: $dark !important;
          font-weight: 600;
          opacity: 0.8 !important;
          &.active{
            background-color: transparent !important;
          }
        }
      }
      li{
        .has-arrow::after{
          border-color:$dark !important;
        }
        .has-arrow.active::after{
          border-color:$dark !important;
        }
      }
    }
  }   
}

// Down LG Screen
@include media-breakpoint-down(xl) {
  .left-sidebar {
    width: $sidebar-width-full;
    position: fixed;
    left: -$sidebar-width-full;
    height: 100%;
  }

  #main-wrapper {
    &.show-sidebar {
      .left-sidebar {
        left: 0;
      }
    }
  }

  .show-sidebar+.dark-transparent {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 8;
  }
}


// User Profile styles moved to components/_user-profile.scss

@import "./sidebar-arrow";