import React, { useEffect } from 'react';
import { usePageMeta } from '../context/PageContext';
import { PermissionLevel } from '../config/authConfig';
export const menuStructure = {
  'HOME': ['Dashboard', 'EstadoDeCuenta', 'Estadodecuenta', 'BlankPage'],
  'CONFIGURACIÓN': ['Settings']
};

export const getPageSectionInfo = (key: string): { section: string, order: number } => {
  for (const [section, pages] of Object.entries(menuStructure)) {
    const index = pages.indexOf(key);
    if (index !== -1) {
      return { section, order: index };
    }
    const lowerKey = key.toLowerCase();
    const caseInsensitiveIndex = pages.findIndex(p => p.toLowerCase() === lowerKey);
    if (caseInsensitiveIndex !== -1) {
      return { section, order: caseInsensitiveIndex };
    }
  }
  return { section: 'HOME', order: 999 };
};


export interface PagePermissions {
  requiredPermission: PermissionLevel;
  resourceId: string;
}

export interface PageMetadata {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  showInMenu?: boolean;
  menuOrder?: number;
  path?: string;
  requiresAuth?: boolean;
  permissions?: PagePermissions;
  parent?: string;
  section?: string;
}

type ComponentWithMetadata<P = Record<string, unknown>> = React.ComponentType<P> & {
  pageMetadata?: PageMetadata;
};

export const withPageMetadata = <P extends object>(
  Component: ComponentWithMetadata<P>
) => {
  const WithPageMetadata = (props: P) => {
    const { setPageMeta } = usePageMeta();
    const metadata = Component.pageMetadata;
    useEffect(() => {
      if (metadata) {
        setPageMeta({
          title: metadata.title,
          description: metadata.description,
          icon: metadata.icon
        });
      }
    }, []);
    return <Component {...props} />;
  };
  WithPageMetadata.displayName = `WithPageMetadata(${Component.displayName || Component.name || 'Component'})`;
  return WithPageMetadata;
};

export default withPageMetadata;
