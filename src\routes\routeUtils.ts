import { MAIN_ROUTE_PREFIX, AUTH_ROUTE_PREFIX } from './routeConstants';
import { pageMetadata, registerPageMetadata, registerPageModule, PageMetadata } from './PageMetadata';

export const loadPageMetadata = async (): Promise<void> => {

  try {
    const pageModules = import.meta.glob(['../pages/**/*.tsx']);
    const indexModules = import.meta.glob(['../pages/**/index.ts']);
    const registeredModules = new Set<string>();
    for (const path in indexModules) {
      try {
        const module = await indexModules[path]() as any;

        if (module.pageMetadata) {
          const pathParts = path.split('/');
          const dirName = pathParts[pathParts.length - 2]; 

          if (registeredModules.has(dirName)) {
            continue;
          }

          registerPageMetadata(dirName, module.pageMetadata);

          if (module.default) {
            try {
              registerPageModule(dirName, module.default, module.pageMetadata);

              registeredModules.add(dirName);
            } catch (error) {
              console.error(`[ROUTES] Error registering page module: ${dirName}`, error);
            }
          } else {
            console.warn(`[ROUTES] Index module has metadata but no default export: ${path}`);
          }
        }
      } catch (error) {
        console.error(`[ROUTES] Error loading index module: ${path}`, error);
      }
    }

    for (const path in pageModules) {
      try {
        const pathParts = path.split('/');
        const dirName = pathParts[pathParts.length - 2];
        const fileName = pathParts[pathParts.length - 1].replace('.tsx', '');

        const indexPath = path.replace(fileName + '.tsx', 'index.ts');
        if (fileName === dirName && indexModules[indexPath]) {
          continue;
        }
        if (fileName === dirName && registeredModules.has(dirName)) {
          continue;
        }

        const module = await pageModules[path]() as any;

        if (module.pageMetadata) {
          let key = '';

          if (pathParts.length >= 3) {
            if (fileName === dirName) {
              key = dirName;
            } else {
              key = fileName;
            }
          } else {
            key = path.split('/').pop()?.replace('.tsx', '') || '';
          }

          if (registeredModules.has(key)) {
            continue;
          }
          registerPageMetadata(key, module.pageMetadata);

          if (module.default) {
            try {
              registerPageModule(key, module.default, module.pageMetadata);
              registeredModules.add(key);
            } catch (error) {
              console.error(`[ROUTES] Error registering page module: ${key}`, error);
            }
          } else {
            console.warn(`[ROUTES] Module has metadata but no default export: ${path}`);
          }
        }
      } catch (error) {
        console.error(`[ROUTES] Error loading page module: ${path}`, error);
      }
    }

  } catch (error) {
    console.error('[ROUTES] Error loading page metadata:', error);
  }
};

export const getRoutesByPrefix = (prefix: string): PageMetadata[] => {
  return Object.values(pageMetadata).filter(p => p.path.startsWith(prefix));
};

export const getProtectedRoutes = (): PageMetadata[] => {
  return getRoutesByPrefix(MAIN_ROUTE_PREFIX);
};

export const getAuthRoutes = (): PageMetadata[] => {
  return getRoutesByPrefix(AUTH_ROUTE_PREFIX);
};

export default {
  loadPageMetadata,
  getRoutesByPrefix,
  getProtectedRoutes,
  getAuthRoutes
};
